from playwright.sync_api import sync_playwright
import time
import subprocess
import os
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('linkedin_search.log'),
        logging.StreamHandler()
    ]
)

def check_linkedin_login_status(page):
    """Check if user is already logged into LinkedIn"""
    try:
        logging.info("Detecting login status...")
        
        logged_in_selectors = [
            ".global-nav__me",
            ".global-nav__me-photo", 
            ".search-global-typeahead",
            ".global-nav__primary-items",
            ".feed-shared-update-v2"
        ]
        
        for selector in logged_in_selectors:
            try:
                element = page.wait_for_selector(selector, timeout=3000)
                if element and element.is_visible():
                    logging.info(f"Login detected via selector: {selector}")
                    return True
            except:
                continue
        
        # Check for login form (indicates NOT logged in)
        login_form_selectors = [
            "input[name='session_key']",
            "input[name='session_password']",
            ".login__form"
        ]
        
        for selector in login_form_selectors:
            try:
                element = page.wait_for_selector(selector, timeout=2000)
                if element and element.is_visible():
                    logging.warning(f"Login form detected - not logged in")
                    return False
            except:
                continue
        
        current_url = page.url
        if "/login" in current_url or "/checkpoint" in current_url:
            logging.warning(f"Login required - URL: {current_url}")
            return False
        elif "/feed" in current_url:
            logging.info(f"Likely logged in - URL: {current_url}")
            return True
        
        logging.warning("Login status unclear - assuming not logged in")
        return False
        
    except Exception as e:
        logging.error(f"Error checking login status: {e}")
        return False

def is_chrome_debug_running(port="9222"):
    """Check if Chrome is already running with debugging port"""
    try:
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True, shell=True)
        return f':{port}' in result.stdout
    except:
        return False

def start_chrome_if_needed():
    """Start Chrome with debugging if not already running"""
    chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
    debug_port = "9222"
    
    if is_chrome_debug_running(debug_port):
        logging.info("Chrome with debugging is already running")
        return True
    
    try:
        logging.info("Starting Chrome with debugging...")
        subprocess.Popen([
            chrome_path,
            f"--remote-debugging-port={debug_port}",
            "--disable-blink-features=AutomationControlled",
            "--disable-web-security",
            "--user-data-dir=C:\\temp\\chrome_debug_linkedin"
        ])
        time.sleep(5)  # Give Chrome time to start
        return True
    except Exception as e:
        logging.error(f"Failed to start Chrome: {e}")
        return False

def main():
    logging.info("=== LinkedIn Profile Search - Automated Run ===")
    
    # Ensure Chrome is running
    if not start_chrome_if_needed():
        logging.error("Could not start Chrome. Exiting.")
        return False
    
    try:
        with sync_playwright() as p:
            # Connect to Chrome
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            logging.info("Connected to Chrome browser")
            
            # Get or create page
            contexts = browser.contexts
            if contexts and contexts[0].pages:
                page = contexts[0].pages[0]
                logging.info("Using existing Chrome tab")
            else:
                context = browser.new_context()
                page = context.new_page()
                logging.info("Created new tab")
            
            # Navigate to LinkedIn
            logging.info("Navigating to LinkedIn...")
            page.goto("https://www.linkedin.com/feed/", timeout=60000)
            page.wait_for_load_state("domcontentloaded", timeout=30000)
            
            # Check login status
            if not check_linkedin_login_status(page):
                logging.error("Not logged in to LinkedIn. Manual login required.")
                return False
            
            logging.info("Already logged in to LinkedIn!")
            
            # Perform search
            logging.info("Performing search for 'Raja Manohar'...")
            
            search_selectors = [
                "input[placeholder*='Search']",
                ".search-global-typeahead__input",
                "input[aria-label*='Search']"
            ]
            
            search_box = None
            for selector in search_selectors:
                try:
                    search_box = page.wait_for_selector(selector, timeout=5000)
                    if search_box:
                        logging.info(f"Found search box: {selector}")
                        break
                except:
                    continue
            
            if search_box:
                search_box.click()
                search_box.fill("")
                search_box.fill("Raja Manohar")
                page.keyboard.press("Enter")
                logging.info("Search submitted")
                
                # Wait for results
                page.wait_for_load_state("domcontentloaded", timeout=30000)
                time.sleep(3)
                
                # Look for profile links
                profile_selectors = [
                    "a[href*='/in/']",
                    ".search-result__result-link"
                ]
                
                for selector in profile_selectors:
                    try:
                        links = page.query_selector_all(selector)
                        if links:
                            logging.info(f"Found {len(links)} profile links")
                            links[0].click()
                            page.wait_for_load_state("domcontentloaded", timeout=30000)
                            logging.info("Successfully clicked on profile!")
                            return True
                    except Exception as e:
                        logging.error(f"Error clicking profile: {e}")
                        continue
                
                logging.warning("Could not find or click profile links")
                return False
            else:
                logging.error("Could not find search box")
                return False
                
    except Exception as e:
        logging.error(f"Error during automation: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        logging.info("LinkedIn profile search completed successfully")
    else:
        logging.error("LinkedIn profile search failed")
