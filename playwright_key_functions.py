from playwright.async_api import async_playwright
import asyncio

async def playwright_example():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()

        #navigating to Google Search

        await page.goto("https://google.com/search?q=playwright+example")
        await page.wait_for_timeout(5000)  # Wait for 1 second before taking the screenshot
        await page.screenshot(path="google.png")
        
        print("Screenshot saved to google.png")

        #CSS Selector and xpath example

        print("execution completed")

       # await browser.close()

if __name__ == "__main__":
    asyncio.run(playwright_example())