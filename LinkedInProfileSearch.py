from playwright.sync_api import sync_playwright
import os
import time
import subprocess


CHROME_PATH = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
# Start with LinkedIn login page instead of feed
LINKEDIN_URL = "https://www.linkedin.com/login"

def start_chrome_with_debugging():
    """Start Chrome with remote debugging if not already running"""
    try:
        # Check if Chrome is already running with debugging port
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True, shell=True)
        if ':9222' in result.stdout:
            print("Chrome with debugging port is already running!")
            return True
    except:
        pass

    try:
        # Start Chrome with remote debugging
        print("Starting Chrome with remote debugging...")
        subprocess.Popen([
            CHROME_PATH,
            "--remote-debugging-port=9222",
            "--disable-blink-features=AutomationControlled",
            "--disable-web-security",
            "--user-data-dir=C:\\temp\\chrome_debug"
        ])
        time.sleep(3)  # Give Chrome time to start
        return True
    except Exception as e:
        print(f"Failed to start Chrome: {e}")
        return False

def main():
    with sync_playwright() as p:
        try:
            # First, try to connect to existing Chrome with debugging port
            browser = None
            try:
                print("Attempting to connect to existing Chrome browser...")
                browser = p.chromium.connect_over_cdp("http://localhost:9222")
                print("✅ Successfully connected to existing Chrome browser!")
            except Exception as e:
                print(f"Could not connect to existing Chrome: {e}")
                print("Starting new Chrome instance with debugging...")

                # Start Chrome with debugging port
                if not start_chrome_with_debugging():
                    print("Failed to start Chrome. Falling back to Playwright's Chromium...")
                    browser = p.chromium.launch(
                        headless=False,
                        args=[
                            "--disable-blink-features=AutomationControlled",
                            "--disable-web-security"
                        ]
                    )
                else:
                    # Try to connect again after starting Chrome
                    time.sleep(2)
                    try:
                        browser = p.chromium.connect_over_cdp("http://localhost:9222")
                        print("✅ Successfully connected to newly started Chrome!")
                    except:
                        print("Still couldn't connect. Using Playwright's Chromium...")
                        browser = p.chromium.launch(headless=False)

            # Get existing contexts or create new one
            contexts = browser.contexts
            if contexts:
                # Use existing context (existing Chrome tab/window)
                context = contexts[0]
                pages = context.pages
                if pages:
                    page = pages[0]  # Use existing tab
                    print("Using existing Chrome tab")
                else:
                    page = context.new_page()
                    print("Created new tab in existing Chrome window")
            else:
                # Create new context if none exists
                context = browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                )
                page = context.new_page()
                print("Created new context and tab")

            print("Opening LinkedIn login page...")
            # Open LinkedIn login page first
            page.goto(LINKEDIN_URL, timeout=60000)  # Increased timeout to 60 seconds

            # Wait for page to load with increased timeout
            try:
                page.wait_for_load_state("domcontentloaded", timeout=30000)
                print("LinkedIn login page loaded successfully!")

                # Wait for login form to be visible
                page.wait_for_selector("input[name='session_key']", timeout=10000)
                print("Login form detected. Please log in manually in the browser window.")

                # Wait for user to log in manually
                print("Please log in to LinkedIn in the browser window, then press Enter here to continue...")
                input()

                # After login, navigate to LinkedIn home/feed
                print("Navigating to LinkedIn feed...")
                page.goto("https://www.linkedin.com/feed/", timeout=60000)
                page.wait_for_load_state("domcontentloaded", timeout=30000)

                # Search for 'Raja Manohar'
                print("Searching for 'Raja Manohar'...")
                search_selector = "input[placeholder*='Search']"

                # Wait for search box and fill it
                page.wait_for_selector(search_selector, timeout=10000)
                page.fill(search_selector, "Raja Manohar")
                page.keyboard.press("Enter")

                # Wait for search results
                page.wait_for_load_state("domcontentloaded", timeout=30000)
                print("Search completed!")

                # Look for profile links in search results
                print("Looking for profile links...")
                time.sleep(3)  # Give time for results to load

                # Try different selectors for profile links
                profile_selectors = [
                    "a[href*='/in/']",
                    ".search-result__result-link",
                    "[data-control-name='search_srp_result']"
                ]

                profile_links = []
                for selector in profile_selectors:
                    profile_links = page.query_selector_all(selector)
                    if profile_links:
                        print(f"Found {len(profile_links)} potential profile links using selector: {selector}")
                        break

                if profile_links:
                    print("Clicking on the first profile link...")
                    profile_links[0].click()
                    page.wait_for_load_state("domcontentloaded", timeout=30000)
                    print("Successfully navigated to profile!")
                else:
                    print("No profile links found. You may need to manually click on the profile.")

            except Exception as e:
                print(f"Error during page interaction: {str(e)}")
                print("The browser window is still open. You can manually navigate and search.")

            print("Script completed. Press Enter to finish (Chrome will remain open)...")
            input()

        except Exception as e:
            print(f"Error launching browser or loading page: {str(e)}")
            print("Please check your internet connection and try again.")

        finally:
            # Don't close browser if we connected to existing Chrome
            # Only close if we launched a new Playwright browser
            try:
                if browser and hasattr(browser, '_connection') and browser._connection:
                    # This is a connected browser, don't close it
                    print("Disconnecting from Chrome (Chrome will remain open)")
                else:
                    # This is a launched browser, safe to close
                    browser.close()
                    print("Closed Playwright browser")
            except:
                pass

if __name__ == "__main__":
    main()
