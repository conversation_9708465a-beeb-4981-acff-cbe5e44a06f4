from playwright.sync_api import sync_playwright
import os

CHROME_PATH = r"C:\\Program Files\\Google\\Chrome\Application\\chrome.exe"
LINKEDIN_URL = "https://www.linkedin.com/feed/"

def main():
    with sync_playwright() as p:
        # Launch Chrome if not already open
        browser = p.chromium.launch(
            headless=False,
            executable_path=CHROME_PATH,
            args=["--remote-debugging-port=9222"]
        )
        context = browser.new_context()
        page = context.new_page()

        # Open LinkedIn
        page.goto(LINKEDIN_URL)
        page.wait_for_load_state("networkidle")

        # Search for '<PERSON>'
        search_selector = "input[placeholder='Search']"
        page.fill(search_selector, "<PERSON>")
        page.keyboard.press("Enter")
        page.wait_for_load_state("networkidle")

        # Click on the profile link (first result)
        page.wait_for_selector("a[href*='/in/']")
        profile_links = page.query_selector_all("a[href*='/in/']")
        if profile_links:
            profile_links[0].click()
            page.wait_for_load_state("networkidle")
            print("Navigated to <PERSON>'s profile.")
        else:
            print("Profile not found.")

        input("Press Enter to exit...")
        browser.close()

if __name__ == "__main__":
    main()
