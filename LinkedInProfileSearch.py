from playwright.sync_api import sync_playwright
import os
import time

CHROME_PATH = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
# Start with LinkedIn login page instead of feed
LINKEDIN_URL = "https://www.linkedin.com/login"

def main():
    with sync_playwright() as p:
        try:
            # Launch Chrome with better configuration
            browser = p.chromium.launch(
                headless=False,
                executable_path=CHROME_PATH,
                args=[
                    "--remote-debugging-port=9222",
                    "--disable-blink-features=AutomationControlled",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor"
                ]
            )

            # Create context with user agent to appear more like a real browser
            context = browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            page = context.new_page()

            print("Opening LinkedIn login page...")
            # Open LinkedIn login page first
            page.goto(LINKEDIN_URL, timeout=60000)  # Increased timeout to 60 seconds

            # Wait for page to load with increased timeout
            try:
                page.wait_for_load_state("domcontentloaded", timeout=30000)
                print("LinkedIn login page loaded successfully!")

                # Wait for login form to be visible
                page.wait_for_selector("input[name='session_key']", timeout=10000)
                print("Login form detected. Please log in manually in the browser window.")

                # Wait for user to log in manually
                print("Please log in to LinkedIn in the browser window, then press Enter here to continue...")
                input()

                # After login, navigate to LinkedIn home/feed
                print("Navigating to LinkedIn feed...")
                page.goto("https://www.linkedin.com/feed/", timeout=60000)
                page.wait_for_load_state("domcontentloaded", timeout=30000)

                # Search for 'Raja Manohar'
                print("Searching for 'Raja Manohar'...")
                search_selector = "input[placeholder*='Search']"

                # Wait for search box and fill it
                page.wait_for_selector(search_selector, timeout=10000)
                page.fill(search_selector, "Raja Manohar")
                page.keyboard.press("Enter")

                # Wait for search results
                page.wait_for_load_state("domcontentloaded", timeout=30000)
                print("Search completed!")

                # Look for profile links in search results
                print("Looking for profile links...")
                time.sleep(3)  # Give time for results to load

                # Try different selectors for profile links
                profile_selectors = [
                    "a[href*='/in/']",
                    ".search-result__result-link",
                    "[data-control-name='search_srp_result']"
                ]

                profile_links = []
                for selector in profile_selectors:
                    profile_links = page.query_selector_all(selector)
                    if profile_links:
                        print(f"Found {len(profile_links)} potential profile links using selector: {selector}")
                        break

                if profile_links:
                    print("Clicking on the first profile link...")
                    profile_links[0].click()
                    page.wait_for_load_state("domcontentloaded", timeout=30000)
                    print("Successfully navigated to profile!")
                else:
                    print("No profile links found. You may need to manually click on the profile.")

            except Exception as e:
                print(f"Error during page interaction: {str(e)}")
                print("The browser window is still open. You can manually navigate and search.")

            print("Script completed. Press Enter to close the browser...")
            input()

        except Exception as e:
            print(f"Error launching browser or loading page: {str(e)}")
            print("Please check your internet connection and try again.")

        finally:
            try:
                browser.close()
            except:
                pass

if __name__ == "__main__":
    main()
