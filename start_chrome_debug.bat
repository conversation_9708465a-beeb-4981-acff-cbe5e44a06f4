@echo off
echo Starting Chrome with debugging for LinkedIn automation...

REM Check if Chrome with debugging is already running
netstat -an | findstr ":9222" >nul
if %errorlevel% == 0 (
    echo Chrome with debugging is already running
    exit /b 0
)

REM Start Chrome with debugging
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --disable-blink-features=AutomationControlled --disable-web-security --user-data-dir=C:\temp\chrome_debug_linkedin

echo Chrome started with debugging enabled
