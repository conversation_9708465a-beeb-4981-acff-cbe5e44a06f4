from playwright.sync_api import sync_playwright
import time
import subprocess
import os

def check_linkedin_login_status(page):
    """
    Check if user is already logged into LinkedIn by detecting authenticated elements.
    Returns True if logged in, False otherwise.
    """
    try:
        print("🔍 Detecting login status...")

        # List of selectors that indicate user is logged in
        logged_in_selectors = [
            # Navigation elements that appear when logged in
            ".global-nav__me",  # User profile menu
            ".global-nav__me-photo",  # User avatar
            "[data-control-name='nav.settings_and_privacy']",  # Settings menu

            # Feed-specific elements
            ".feed-shared-update-v2",  # Feed posts
            ".share-box-feed-entry",  # Share box at top of feed
            ".feed-shared-actor",  # Post authors in feed

            # Search elements that appear when logged in
            ".search-global-typeahead",  # Main search box
            "input[placeholder*='Search']",  # Search input

            # Navigation bar elements
            ".global-nav__primary-items",  # Main navigation
            "[data-control-name='nav.messaging']",  # Messages icon
            "[data-control-name='nav.notifications']",  # Notifications icon

            # User-specific elements
            ".artdeco-dropdown__trigger--placement-bottom",  # Dropdown menus
            ".global-nav__me-content"  # User menu content
        ]

        # Check for login indicators
        for selector in logged_in_selectors:
            try:
                element = page.wait_for_selector(selector, timeout=3000)
                if element and element.is_visible():
                    print(f"✅ Login detected via selector: {selector}")
                    return True
            except:
                continue

        # Additional check: look for login form elements (indicates NOT logged in)
        login_form_selectors = [
            "input[name='session_key']",  # Username field
            "input[name='session_password']",  # Password field
            "button[data-litms-control-urn*='signin-submit']",  # Sign in button
            ".login__form",  # Login form container
            "#username",  # Alternative username field
            "#password"   # Alternative password field
        ]

        for selector in login_form_selectors:
            try:
                element = page.wait_for_selector(selector, timeout=2000)
                if element and element.is_visible():
                    print(f"❌ Login form detected via selector: {selector}")
                    return False
            except:
                continue

        # Check current URL for additional context
        current_url = page.url
        if "/login" in current_url or "/checkpoint" in current_url:
            print(f"❌ Login required - URL indicates login page: {current_url}")
            return False
        elif "/feed" in current_url or "/in/" in current_url:
            print(f"✅ Likely logged in - URL indicates authenticated page: {current_url}")
            # Do one more check for any visible content
            try:
                # Look for any content that suggests we're on an authenticated page
                content_check = page.wait_for_selector("main, .application-outlet, .global-nav", timeout=3000)
                if content_check:
                    return True
            except:
                pass

        print("⚠️  Login status unclear - assuming not logged in")
        return False

    except Exception as e:
        print(f"⚠️  Error checking login status: {e}")
        print("Assuming not logged in for safety")
        return False

def is_chrome_debug_running(port="9222"):
    """Check if Chrome is already running with debugging port"""
    try:
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True, shell=True)
        return f':{port}' in result.stdout
    except:
        return False

def main():
    print("=== LinkedIn Profile Search (Using Existing Chrome) ===")
    print()

    chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
    debug_port = "9222"

    # Step 1: Check if Chrome with debugging is already running
    print("STEP 1: Checking for existing Chrome with debugging...")
    if is_chrome_debug_running(debug_port):
        print("✅ Chrome with debugging is already running!")
        print("Using existing Chrome instance...")
    else:
        print("❌ Chrome with debugging not detected")
        print("\nSTEP 1a: Prepare your Chrome browser")
        print("Please close ALL Chrome windows completely, then press Enter")
        print("This will start Chrome with debugging enabled")
        input("Press Enter to start Chrome with debugging...")

        # Step 2: Start Chrome with debugging
        print(f"Starting Chrome with debugging on port {debug_port}...")
        try:
            # Start Chrome with remote debugging
            subprocess.Popen([
                chrome_path,
                f"--remote-debugging-port={debug_port}",
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--user-data-dir=C:\\temp\\chrome_debug_linkedin"
            ])
            print("✅ Chrome started with debugging enabled!")
            time.sleep(3)  # Give Chrome time to start
        except Exception as e:
            print(f"❌ Failed to start Chrome: {e}")
            return
    
    # Step 2: Connect to Chrome using Playwright
    print("\nSTEP 2: Connecting to Chrome...")
    with sync_playwright() as p:
        try:
            # Connect to the Chrome instance we just started
            browser = p.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            print("✅ Successfully connected to your Chrome browser!")
            
            # Get the first page/tab
            contexts = browser.contexts
            if contexts and contexts[0].pages:
                page = contexts[0].pages[0]
                print("✅ Using existing Chrome tab")
            else:
                # Create new tab if none exists
                context = browser.new_context()
                page = context.new_page()
                print("✅ Created new tab in Chrome")
            
            # Step 3: Navigate to LinkedIn and check login status
            print("\nSTEP 3: Navigating to LinkedIn...")
            page.goto("https://www.linkedin.com/feed/", timeout=60000)
            page.wait_for_load_state("domcontentloaded", timeout=30000)
            print("✅ LinkedIn page loaded!")

            # Step 4: Check if already logged in
            print("\nSTEP 4: Checking login status...")
            try:
                is_logged_in = check_linkedin_login_status(page)
            except Exception as e:
                print(f"⚠️  Error during login detection: {e}")
                print("Assuming not logged in and proceeding with manual login...")
                is_logged_in = False

            if is_logged_in:
                print("✅ Already logged in to LinkedIn!")
                print("Proceeding directly to search functionality...")
            else:
                print("❌ Not logged in to LinkedIn")
                print("\nSTEP 4a: Manual Login Required")
                print("You will be redirected to the login page.")
                print("Please log in to LinkedIn and then come back here.")

                try:
                    # Redirect to login page
                    page.goto("https://www.linkedin.com/login", timeout=60000)
                    page.wait_for_load_state("domcontentloaded", timeout=30000)
                    print("✅ LinkedIn login page loaded!")

                    input("Press Enter after you've logged in to LinkedIn...")

                    # Navigate back to feed after login
                    print("Navigating back to LinkedIn feed...")
                    page.goto("https://www.linkedin.com/feed/", timeout=60000)
                    page.wait_for_load_state("domcontentloaded", timeout=30000)

                    # Verify login was successful
                    try:
                        if check_linkedin_login_status(page):
                            print("✅ Login verification successful!")
                        else:
                            print("⚠️  Login verification failed, but continuing anyway...")
                            print("You may need to manually navigate or search.")
                    except Exception as e:
                        print(f"⚠️  Error verifying login: {e}")
                        print("Continuing anyway...")

                except Exception as e:
                    print(f"❌ Error during login process: {e}")
                    print("Please manually navigate to LinkedIn and log in.")
                    print("Then manually search for 'Raja Manohar'.")
                    return

            # Step 6: Perform search
            print("\nSTEP 5: Performing search...")
            try:
                # Go to LinkedIn feed
                page.goto("https://www.linkedin.com/feed/", timeout=60000)
                page.wait_for_load_state("domcontentloaded", timeout=30000)
                print("✅ LinkedIn feed loaded!")
                
                # Find and use search box
                search_selectors = [
                    "input[placeholder*='Search']",
                    "input[aria-label*='Search']",
                    ".search-global-typeahead__input",
                    "input[data-control-name='nav.search']"
                ]
                
                search_box = None
                for selector in search_selectors:
                    try:
                        search_box = page.wait_for_selector(selector, timeout=5000)
                        if search_box:
                            print(f"✅ Found search box with selector: {selector}")
                            break
                    except:
                        continue
                
                if search_box:
                    # Clear and fill search box
                    search_box.click()
                    search_box.fill("")  # Clear first
                    search_box.fill("RajaManohar")
                    print("✅ Entered 'RajaManohar' in search box")
                    
                    # Press Enter to search
                    page.keyboard.press("Enter")
                    print("✅ Search submitted!")
                    
                    # Wait for search results
                    page.wait_for_load_state("domcontentloaded", timeout=30000)
                    time.sleep(3)  # Additional wait for results to load
                    
                    # Look for profile links
                    print("🔍 Looking for profile links...")
                    profile_selectors = [
                        "a[href*='/in/'][href*='raja'][href*='manohar']",
                        "a[href*='/in/']",
                        ".search-result__result-link",
                        "[data-control-name='search_srp_result'] a"
                    ]
                    
                    profile_found = False
                    for selector in profile_selectors:
                        try:
                            links = page.query_selector_all(selector)
                            if links:
                                print(f"✅ Found {len(links)} potential profile links")
                                # Click the first one
                                links[0].click()
                                page.wait_for_load_state("domcontentloaded", timeout=30000)
                                print("✅ Clicked on first profile link!")
                                profile_found = True
                                break
                        except Exception as e:
                            print(f"Selector {selector} failed: {e}")
                            continue
                    
                    if not profile_found:
                        print("⚠️  Could not automatically click profile. Please manually click on Raja Manohar's profile in the browser.")
                    
                else:
                    print("❌ Could not find search box. Please manually search for 'Raja Manohar' in the browser.")
                    
            except Exception as e:
                print(f"❌ Error during search: {e}")
                print("You can manually search for 'Raja Manohar' in the browser window.")
            
            # Step 7: Keep browser open
            print("\n✅ Script completed successfully!")
            print("Your Chrome browser will remain open with the LinkedIn page.")
            print("Press Enter to disconnect the script (Chrome will stay open)...")
            input()
            
        except Exception as e:
            print(f"❌ Failed to connect to Chrome: {e}")
            print("Make sure Chrome is running with debugging enabled.")
        
        # Note: We don't close the browser since it's the user's Chrome instance

if __name__ == "__main__":
    main()
