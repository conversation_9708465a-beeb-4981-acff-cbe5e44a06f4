from playwright.sync_api import sync_playwright
import time
import subprocess
import os

def main():
    print("=== LinkedIn Profile Search (Using Existing Chrome) ===")
    print()
    
    # Step 1: Instructions for user to prepare Chrome
    print("STEP 1: Prepare your Chrome browser")
    print("1. Close ALL Chrome windows completely")
    print("2. Press Enter here to start Chrome with debugging enabled")
    input("Press Enter to continue...")
    
    # Step 2: Start Chrome with debugging
    chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
    debug_port = "9222"
    
    print(f"Starting Chrome with debugging on port {debug_port}...")
    try:
        # Start Chrome with remote debugging
        subprocess.Popen([
            chrome_path,
            f"--remote-debugging-port={debug_port}",
            "--disable-blink-features=AutomationControlled",
            "--disable-web-security",
            "--user-data-dir=C:\\temp\\chrome_debug_linkedin"
        ])
        print("✅ Chrome started with debugging enabled!")
        time.sleep(3)  # Give Chrome time to start
    except Exception as e:
        print(f"❌ Failed to start Chrome: {e}")
        return
    
    # Step 3: Connect to Chrome using Playwright
    print("\nSTEP 2: Connecting to Chrome...")
    with sync_playwright() as p:
        try:
            # Connect to the Chrome instance we just started
            browser = p.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            print("✅ Successfully connected to your Chrome browser!")
            
            # Get the first page/tab
            contexts = browser.contexts
            if contexts and contexts[0].pages:
                page = contexts[0].pages[0]
                print("✅ Using existing Chrome tab")
            else:
                # Create new tab if none exists
                context = browser.new_context()
                page = context.new_page()
                print("✅ Created new tab in Chrome")
            
            # Step 4: Navigate to LinkedIn
            print("\nSTEP 3: Navigating to LinkedIn...")
            page.goto("https://www.linkedin.com/login", timeout=60000)
            page.wait_for_load_state("domcontentloaded", timeout=30000)
            print("✅ LinkedIn login page loaded!")
            
            # Step 5: Wait for manual login
            print("\nSTEP 4: Manual Login Required")
            print("Please log in to LinkedIn in the Chrome window that just opened.")
            print("After logging in successfully, come back here and press Enter.")
            input("Press Enter after you've logged in to LinkedIn...")
            
            # Step 6: Navigate to feed and search
            print("\nSTEP 5: Performing search...")
            try:
                # Go to LinkedIn feed
                page.goto("https://www.linkedin.com/feed/", timeout=60000)
                page.wait_for_load_state("domcontentloaded", timeout=30000)
                print("✅ LinkedIn feed loaded!")
                
                # Find and use search box
                search_selectors = [
                    "input[placeholder*='Search']",
                    "input[aria-label*='Search']",
                    ".search-global-typeahead__input",
                    "input[data-control-name='nav.search']"
                ]
                
                search_box = None
                for selector in search_selectors:
                    try:
                        search_box = page.wait_for_selector(selector, timeout=5000)
                        if search_box:
                            print(f"✅ Found search box with selector: {selector}")
                            break
                    except:
                        continue
                
                if search_box:
                    # Clear and fill search box
                    search_box.click()
                    search_box.fill("")  # Clear first
                    search_box.fill("RajaManohar")
                    print("✅ Entered 'RajaManohar' in search box")
                    
                    # Press Enter to search
                    page.keyboard.press("Enter")
                    print("✅ Search submitted!")
                    
                    # Wait for search results
                    page.wait_for_load_state("domcontentloaded", timeout=30000)
                    time.sleep(3)  # Additional wait for results to load
                    
                    # Look for profile links
                    print("🔍 Looking for profile links...")
                    profile_selectors = [
                        "a[href*='/in/'][href*='raja'][href*='manohar']",
                        "a[href*='/in/']",
                        ".search-result__result-link",
                        "[data-control-name='search_srp_result'] a"
                    ]
                    
                    profile_found = False
                    for selector in profile_selectors:
                        try:
                            links = page.query_selector_all(selector)
                            if links:
                                print(f"✅ Found {len(links)} potential profile links")
                                # Click the first one
                                links[0].click()
                                page.wait_for_load_state("domcontentloaded", timeout=30000)
                                print("✅ Clicked on first profile link!")
                                profile_found = True
                                break
                        except Exception as e:
                            print(f"Selector {selector} failed: {e}")
                            continue
                    
                    if not profile_found:
                        print("⚠️  Could not automatically click profile. Please manually click on Raja Manohar's profile in the browser.")
                    
                else:
                    print("❌ Could not find search box. Please manually search for 'Raja Manohar' in the browser.")
                    
            except Exception as e:
                print(f"❌ Error during search: {e}")
                print("You can manually search for 'Raja Manohar' in the browser window.")
            
            # Step 7: Keep browser open
            print("\n✅ Script completed successfully!")
            print("Your Chrome browser will remain open with the LinkedIn page.")
            print("Press Enter to disconnect the script (Chrome will stay open)...")
            input()
            
        except Exception as e:
            print(f"❌ Failed to connect to Chrome: {e}")
            print("Make sure Chrome is running with debugging enabled.")
        
        # Note: We don't close the browser since it's the user's Chrome instance

if __name__ == "__main__":
    main()
